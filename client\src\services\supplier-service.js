import { authApi } from "@/context/auth-context";

/**
 * Service for supplier-related API calls
 */
export const supplierService = {
  /**
   * Get all suppliers with pagination and search
   * @param {Object} params - Query parameters
   * @param {string} params.query - Search query
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @param {string} params.sortBy - Field to sort by
   * @param {string} params.sortDir - Sort direction ('asc' or 'desc')
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getAllSuppliers: async (params = {}) => {
    try {
      const {
        query = "",
        page = 0,
        size = 6,
        sortBy = "addedDate",
        sortDir = "desc",
      } = params;

      const response = await authApi.get("/api/v1/suppliers", {
        params: {
          query,
          page,
          size,
          sortBy,
          sortDir,
        },
      });

      // Check if the response has the expected structure
      if (!response.data) {
        // Create a standardized response format
        return {
          success: true,
          data: response || [],
          message: "Data retrieved but in unexpected format",
        };
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      throw error;
    }
  },

  /**
   * Add a new supplier
   * @param {Object} supplierData - Supplier data
   * @param {string} supplierData.firstName - First name
   * @param {string} supplierData.lastName - Last name
   * @param {string} supplierData.email - Email address
   * @param {string} supplierData.phoneNumber - Phone number
   * @param {string} supplierData.localization - Localization (address/location)
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  addSupplier: async (supplierData) => {
    try {
      const response = await authApi.post("/api/v1/suppliers", supplierData);

      // Check if the response has the expected structure
      if (!response.data) {
        // Create a standardized response format
        return {
          success: true,
          data: response || {},
          message: "Supplier added but response in unexpected format",
        };
      }

      return response.data;
    } catch (error) {
      console.error("Error adding supplier:", error);
      throw error;
    }
  },

  /**
   * Get a supplier by ID
   * @param {string} supplierId - Supplier ID
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getSupplierById: async (supplierId) => {
    try {
      const response = await authApi.get(`/api/v1/suppliers/${supplierId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching supplier:", error);
      throw error;
    }
  },

  /**
   * Delete a supplier
   * @param {string} supplierId - Supplier ID
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  deleteSupplier: async (supplierId) => {
    try {
      const response = await authApi.delete(`/api/v1/suppliers/${supplierId}`);

      // Check if the response has the expected structure
      if (!response.data) {
        // Create a standardized response format
        return {
          success: true,
          data: {},
          message: "Supplier deleted but response in unexpected format",
        };
      }

      return response.data;
    } catch (error) {
      console.error("Error deleting supplier:", error);
      throw error;
    }
  },
};
